

.attachment-wrap {
  max-height: calc(100vh - 200px - var(--banner-height));
  overflow: scroll;

  .attachment-history-wrap {
    position: relative;

    .devider-wrap {
      padding: var(--spacing-none) var(--spacing-lg) var(--spacing-none) var(--spacing-5xl);
    }

    .attachment-item {
      .header-date-wrap {
        padding: var(--spacing-base) var(--spacing-none);
        position: sticky;
        top: var(--spacing-none);
        background: var(--color-white);

        .header-date {
          font-weight: var(--font-weight-medium);
        }

        .calender-icon {
          background-color: var(--color-white);
          position: absolute;
          left: -24px;
          fill: var(--color-dark-50);
          height: 15px;
          width: 15px;
        }
      }

      .name-text-wrap {
        .file-status-wrap {
          font-weight: var(--font-weight-medium);
        }

        .circle-wrap {
          position: absolute;
          left: 49px;
          fill: var(--color-dark-50);
          height: 5px;
          width: 5px;
        }
      }

      .file-name {
        font-size: var(--font-size-base);

        .file-name-text {
          color: var(--color-dark-50);
          display: inline-block;
          padding-right: var(--spacing-md);
        }
      }

      .time-wrap {
        font-size: var(--font-size-base);
        color: var(--color-dark-50);
      }

      .attachment-type-wrap {
        font-size: var(--font-size-base);

        .attachment-type-text {
          display: inline-block;
          padding-right: var(--spacing-md);
          color: var(--color-dark-50);
        }
      }
    }
  }
}
