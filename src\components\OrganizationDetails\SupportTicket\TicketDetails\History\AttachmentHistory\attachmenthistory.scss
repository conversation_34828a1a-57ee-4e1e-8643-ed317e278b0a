

.attachment-wrap {
  max-height: calc(100vh - 200px - var(--banner-height));
  overflow: scroll;

  .attachment-history-wrap {
    position: relative;

    .devider-wrap {
      padding: var(--spacing-none) var(--spacing-lg) var(--spacing-none) var(--spacing-5xl);
    }

    .attachment-item {
      .header-date-wrap {
        padding: var(--spacing-base) var(--spacing-none);
        position: sticky;
        top: var(--spacing-none);
        background: var(--color-white);

        .header-date {
          font-weight: var(--font-weight-medium);
        }

        .calender-icon {
          background-color: var(--color-white);
          position: absolute;
          left: -24px;
          fill: var(--icon-color-primary);
          height: var(--icon-size-xs);
          width: var(--icon-size-xs);
          border-radius: var(--border-radius-xs);
          padding: var(--spacing-xxs);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          border: 1px solid var(--color-light-grayish-blue);
        }
      }

      .name-text-wrap {
        .file-status-wrap {
          font-weight: var(--font-weight-medium);
        }

        .circle-wrap {
          position: absolute;
          left: 49px;
          fill: var(--icon-color-primary);
          height: var(--spacing-xs);
          width: var(--spacing-xs);
          border-radius: 50%;
          background-color: var(--icon-color-primary);
          box-shadow: 0 0 0 3px var(--color-white), 0 0 0 4px var(--color-light-grayish-blue);
        }
      }

      .file-name {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-xs);

        .file-name-text {
          color: var(--text-color-slate-gray);
          display: inline-block;
          padding-right: var(--spacing-sm);
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .note-content {
          color: var(--text-color-black);
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-regular);
        }
      }

      .time-wrap {
        font-size: var(--font-size-xs);
        color: var(--text-color-slate-gray);
        margin-bottom: var(--spacing-sm);
        font-weight: var(--font-weight-regular);
        opacity: 0.8;
      }

      .attachment-type-wrap {
        font-size: var(--font-size-sm);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        .attachment-type-text {
          display: inline-block;
          color: var(--text-color-slate-gray);
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .status-badge {
          display: inline-block;
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
          text-transform: capitalize;
          border-radius: var(--border-radius-xs);
          padding: var(--spacing-xxs) var(--spacing-sm);
          border: 1px solid transparent;

          // Default styling
          background-color: var(--color-light-grayish-blue);
          color: var(--color-dark-50);
          border-color: var(--color-light-grayish-blue);
        }
      }
    }
  }
}

// Status-specific styling for status badges
.status-badge {
  &.status-yellow {
    background-color: var(--color-light-champagne) !important;
    color: var(--color-muted-mustard) !important;
    border-color: var(--color-muted-mustard) !important;
  }

  &.draft {
    background-color: var(--color-soft-lavender) !important;
    color: var(--text-blue) !important;
    border-color: var(--color-blue) !important;
  }

  &.ongoing {
    background-color: var(--color-white) !important;
    color: var(--text-blue) !important;
    border-color: var(--color-blue) !important;
  }

  &.success {
    background-color: var(--color-light-green) !important;
    color: var(--color-light-success) !important;
    border-color: var(--color-light-success) !important;
  }

  &.closed {
    background-color: var(--color-light-grayish-blue) !important;
    color: var(--color-dark-50) !important;
    border-color: var(--color-dark-50) !important;
  }

  &.status-active {
    background-color: var(--color-success-opacity) !important;
    color: var(--color-success) !important;
    border-color: var(--color-success) !important;
  }

  &.status-inactive {
    background-color: var(--color-danger-background) !important;
    color: var(--text-color-danger) !important;
    border-color: var(--text-color-danger) !important;
  }

  &.failed {
    background-color: var(--color-danger-background) !important;
    color: var(--text-color-danger) !important;
    border-color: var(--text-color-danger) !important;
  }
}
