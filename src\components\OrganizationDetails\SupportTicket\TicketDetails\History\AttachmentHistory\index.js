import React from 'react';
import { Box, Divider, Typography } from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import CircleIcon from '@mui/icons-material/Circle';
import { DateFormat } from '@/helper/common/commonFunctions';
import './attachmenthistory.scss';

const AttachmentHistory = ({ attachmentData }) => {
  // Helper function to format action type text
  const formatActionType = (actionType) => {
    if (!actionType) return '';
    return actionType
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };
  return (
    <Box className="attachment-wrap">
      <Box className="devider-wrap">
        <Divider orientation="vertical" className="vertical-divider" />
      </Box>
      <Box className="attachment-history-wrap">
        {attachmentData?.map((entry, index) => (
          <Box key={index} className="d-flex attachment-history">
            <Box className="devider-wrap">
              <Divider orientation="vertical" className="vertical-divider" />
            </Box>
            <Box className="attachment-item">
              <Box className="d-flex align-center header-date-wrap">
                <CalendarMonthIcon className="calender-icon" />
                <Typography component="p" className="header-date body-semibold">
                  {DateFormat(entry?.created_at, 'datesWithhour')}
                </Typography>
              </Box>
              <Typography component="p" className="time-wrap content-text">
                {DateFormat(entry?.created_at, 'hoursUTC')}
              </Typography>
              <Box className="d-flex align-center name-text-wrap">
                <CircleIcon className="circle-wrap" />
                <Typography className="file-status-wrap body-semibold">
                  {formatActionType(entry?.action_type)}
                </Typography>
              </Box>
              <Typography className="file-name body-text">
                <span className="file-name-text content-text">Note </span>
                {entry?.change_note}
              </Typography>
              <Typography className="attachment-type-wrap body-text">
                <span className="attachment-type-text content-text">
                  Status{' '}
                </span>
                {entry?.new_status}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default AttachmentHistory;
