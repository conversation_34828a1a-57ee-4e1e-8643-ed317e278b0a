import React from 'react';
import { Box, Divider, Typography } from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import CircleIcon from '@mui/icons-material/Circle';
import { DateFormat } from '@/helper/common/commonFunctions';
import './attachmenthistory.scss';

const AttachmentHistory = ({ attachmentData }) => {
  // Helper function to format action type text
  const formatActionType = (actionType) => {
    if (!actionType) return '';
    return actionType
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Helper function to get status class based on status value
  const getStatusClass = (status) => {
    if (!status) return 'draft';
    const statusLower = status.toLowerCase();
    const statusMap = {
      open: 'status-yellow',
      'in progress': 'draft',
      in_progress: 'draft',
      ongoing: 'ongoing',
      resolved: 'success',
      closed: 'closed',
      completed: 'success',
      active: 'status-active',
      inactive: 'status-inactive',
      pending: 'draft',
      approved: 'success',
      rejected: 'failed',
      cancelled: 'failed',
    };
    return statusMap[statusLower] || 'draft';
  };
  return (
    <Box className="attachment-wrap">
      <Box className="devider-wrap">
        <Divider orientation="vertical" className="vertical-divider" />
      </Box>
      <Box className="attachment-history-wrap">
        {attachmentData?.map((entry, index) => (
          <Box key={index} className="d-flex attachment-history">
            <Box className="devider-wrap">
              <Divider orientation="vertical" className="vertical-divider" />
            </Box>
            <Box className="attachment-item">
              <Box className="d-flex align-center header-date-wrap">
                <CalendarMonthIcon className="calender-icon" />
                <Typography component="p" className="header-date body-semibold">
                  {DateFormat(entry?.created_at, 'datesWithhour')}
                </Typography>
              </Box>
              <Typography component="p" className="time-wrap content-text">
                {DateFormat(entry?.created_at, 'hoursUTC')}
              </Typography>
              <Box className="d-flex align-center name-text-wrap">
                <CircleIcon className="circle-wrap" />
                <Typography className="file-status-wrap body-semibold">
                  {formatActionType(entry?.action_type)}
                </Typography>
              </Box>
              <Typography className="file-name body-text">
                <span className="file-name-text content-text">Note </span>
                <span className="note-content">{entry?.change_note}</span>
              </Typography>
              <Typography className="attachment-type-wrap body-text">
                <span className="attachment-type-text content-text">
                  Status{' '}
                </span>
                <span
                  className={`status-badge ${getStatusClass(entry?.new_status)}`}
                >
                  {entry?.new_status}
                </span>
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default AttachmentHistory;
